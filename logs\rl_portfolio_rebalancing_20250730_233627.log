2025-07-30 23:36:27 - rl_portfolio_rebalancing - INFO - setup_logging:129 - Logging to rotating file: logs/rl_portfolio_rebalancing_20250730_233627.log (max 50MB, 5 backups)
2025-07-30 23:36:27 - rl_portfolio_rebalancing - INFO - initialize_unicode_safe_logging_early:4552 -    Configuration mode: auto
2025-07-30 23:36:27 - rl_portfolio_rebalancing - INFO - detect_console_encoding:115 - Encoding detection completed: cp950 (UTF-8: True, Unicode: False, Recommended: safe, Confidence: 0.90)
2025-07-30 23:36:27 - rl_portfolio_rebalancing - INFO - initialize_unicode_safe_logging_early:4570 -    Console encoding: cp950
2025-07-30 23:36:27 - rl_portfolio_rebalancing - INFO - initialize_unicode_safe_logging_early:4573 -    Recommended mode: safe
2025-07-30 23:36:27 - rl_portfolio_rebalancing - INFO - initialize_unicode_safe_logging_early:4574 -    Detection confidence: 0.90
2025-07-30 23:36:27 - rl_portfolio_rebalancing - INFO - initialize_unicode_safe_logging_early:4582 -    Using configured mode: auto
[OK] Unicode-safe logging initialized successfully
   Effective mode: auto
   Character mappings: 166
[MILESTONE] Unicode-safe logging startup completed
Encoding detection completed: cp950 (UTF-8: True, Unicode: False, Recommended: safe, Confidence: 0.90)
[OK] Unicode-safe logging initialized successfully
   Effective mode: auto
   Console encoding: cp950
   Character mappings: 166
ComprehensiveLogger initialized with Unicode-safe logging
SystemErrorHandler initialized
====================================================================================================
RL PORTFOLIO REBALANCING SYSTEM - STARTUP
====================================================================================================
Startup time: 2025-07-30 23:36:27
Python version: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
Working directory: C:\Users\<USER>\Desktop\RL assets

SYSTEM CONFIGURATION
--------------------------------------------------
DATA_CONFIG:
  etf_symbols: ['VT', 'IAGG', 'REET', 'GLD', 'DBA', 'USO', 'UUP']
  risk_free_symbol: ^TNX
  start_date: 2016-01-01
  frequency: M
  data_source: yfinance

TRADING_CONFIG:
  initial_cash: 100000
  transaction_cost: 0.001
  slippage_range: [0.0, 0.01]
  max_allowed_loss: 0.6

TENSORTRADE_CONFIG:
  window_size: 12
  enable_logger: True
  action_scheme: portfolio-weights
  reward_scheme: sharpe-ratio

TRAINING_CONFIG:
  algorithm: PPO
  total_timesteps: 50000
  learning_rate: 0.0003
  batch_size: 64
  policy_layers: [256, 256]
  clip_range: 0.2
  value_function_coeff: 0.5
  entropy_coeff: 0.01

EVALUATION_CONFIG:
  sharpe_window: 12
  benchmark_strategy: equal_weight
  performance_metrics: ['total_return', 'annualized_return', 'volatility', 'sharpe_ratio', 'max_drawdown', 'calmar_ratio']

TECHNICAL_INDICATORS_CONFIG:
  moving_averages: [10, 20, 50]
  rsi_period: 14
  macd_fast: 12
  macd_slow: 26
  macd_signal: 9
  bollinger_period: 20
  bollinger_std: 2

LOGGING_CONFIG:
  level: INFO
  format: %(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s
  date_format: %Y-%m-%d %H:%M:%S
  log_to_file: True
  log_directory: logs
  unicode_mode: auto
  fallback_on_error: True
  debug_encoding_issues: False
  character_mapping: {'[OK]': '[OK]', '[FAIL]': '[FAIL]', '[MILESTONE]': '[MILESTONE]', '[RESOURCE]': '[RESOURCE]', '[PHASE]': '[PHASE]', '[WARNING]': '[WARNING]', '[DATA]': '[DATA]', '[METRICS]': '[METRICS]', '[HEALTH]': '[HEALTH]', '[COUNT]': '[COUNT]'}

DIRECTORY_CONFIG:
  data: data
  models: models
  logs: logs
  results: results
  config: config

ENCODING_STATUS:
  unicode_logging_enabled: True
  encoding_detection_successful: True
  console_encoding: cp950
  supports_utf8: True
  supports_unicode: False
  recommended_mode: safe
  effective_mode: auto
  character_mappings_count: 166
  initialization_error: None
  detection_confidence: 0.8999999999999999

[OK] System startup completed successfully
====================================================================================================
[MILESTONE] MILESTONE: System Initialization
   Timestamp: 2025-07-30 23:36:27
   Elapsed: 0:00:00.019924
   Details:
     timestamp: 20250730_233627
     log_file: logs/rl_portfolio_rebalancing_20250730_233627.log
     directories_created: ['data', 'models', 'logs', 'results', 'config']
     unicode_logging_enabled: True
     console_encoding: cp950
     effective_logging_mode: auto
[RESOURCE] Resource Usage - system_startup:
  Memory: 7.4GB / 7.8GB (94.6%)
  CPU: 56.9%
  Process Memory: 521.4MB
  Disk: 418.7GB / 465.1GB (90.0%)

[PHASE] ==============================================================================
PHASE START: SYSTEM CONFIGURATION
Description: Printing system information and verifying dependencies
Start time: 2025-07-30 23:36:28
================================================================================
================================================================================
SYSTEM CONFIGURATION
================================================================================
ETF Symbols: ['VT', 'IAGG', 'REET', 'GLD', 'DBA', 'USO', 'UUP']
Risk-free rate symbol: ^TNX
Data start date: 2016-01-01
Initial cash: $100,000
Transaction cost: 0.1%
Rebalancing frequency: M
Training timesteps: 50,000
Learning rate: 0.0003
================================================================================
Verifying package dependencies...
[OK] pandas: 2.2.3
[OK] numpy: 1.26.4
[OK] yfinance: 0.2.59
[OK] matplotlib: unknown
[OK] seaborn: 0.13.2
[OK] ta: unknown
[OK] tensortrade: 1.0.4-dev1
[OK] stable-baselines3: 2.6.0
[OK] gym: 0.26.2
All dependencies verified successfully!

[OK] ==============================================================================
PHASE COMPLETED: SYSTEM CONFIGURATION
End time: 2025-07-30 23:36:29
Duration: 0:00:00.006896
Phase Metrics:
  directories_created: 5
  dependencies_verified: True
  logging_configured: True
================================================================================
[MILESTONE] MILESTONE: Project Setup Complete
   Timestamp: 2025-07-30 23:36:29
   Elapsed: 0:00:01.033728
   Details:
     directories: ['data', 'models', 'logs', 'results', 'config']
     log_file: logs/rl_portfolio_rebalancing_20250730_233627.log
[RESOURCE] Resource Usage - after_setup:
  Memory: 7.4GB / 7.8GB (94.6%)
  CPU: 55.9%
  Process Memory: 521.4MB
  Disk: 418.7GB / 465.1GB (90.0%)

[PHASE] ==============================================================================
PHASE START: DATA FETCHING SYSTEM
Description: Testing data fetching, preprocessing, and validation
Start time: 2025-07-30 23:36:30
================================================================================
[MILESTONE] MILESTONE: Data Fetcher Initialization
   Timestamp: 2025-07-30 23:36:30
   Elapsed: 0:00:02.047248
YFinanceDataFetcher initialized with 4-year window
Testing with symbols: ['VT', 'IAGG', 'REET', 'GLD', 'DBA', 'USO', 'UUP']
Using current date for real-time window alignment
Calculated 4-year window: 2021-07-30 to 2025-07-30
[MILESTONE] MILESTONE: Window Dates Calculated
   Timestamp: 2025-07-30 23:36:30
   Elapsed: 0:00:02.048250
   Details:
     window_start: 2021-07-30
     window_end: 2025-07-30
     window_length: 4 years
Fetching ETF data for symbols: ['VT', 'IAGG', 'REET', 'GLD', 'DBA', 'USO', 'UUP']
Window range: 2021-07-30 to 2025-07-30
ETF data validation completed successfully
Successfully fetched data for 7 symbols
Data shape: (48, 35)
Date range: 2021-08-01 00:00:00 to 2025-07-01 00:00:00
Window coverage: 48/48 months (100.0%)
[DATA] DATA QUALITY REPORT
==================================================
[METRICS] Data Shape: (48, 35)
[DATE] Date Range: 2021-08-01 00:00:00 to 2025-07-01 00:00:00
??  Symbols: ['VT', 'IAGG', 'REET', 'GLD', 'DBA', 'USO', 'UUP']
? Missing Data: 0.00%
[DECLINE] Data Gaps: 0
[OK] Data quality assessment: GOOD

[OK] ETF data fetched successfully: (48, 35)
Fetching risk-free rate data for symbol: ^TNX
Window range: 2021-07-30 to 2025-07-30
Insufficient risk-free rate data for full 4-year window: 34 months (minimum recommended: 36)
Risk-free rate data validation completed successfully
Low risk-free rate data coverage: 34/48 months (70.8%)
Successfully fetched risk-free rate data
Data shape: (34, 5)
Date range: 2021-08-01 00:00:00 to 2024-05-01 00:00:00
Window coverage: 34/48 months (70.8%)
[OK] Risk-free rate data fetched successfully: (34, 5)
Ensuring temporal alignment between ETF and risk-free rate data within window
ETF data range: 2021-08-01 00:00:00 to 2025-07-01 00:00:00 (48 months)
Risk-free rate data range: 2021-08-01 00:00:00 to 2024-05-01 00:00:00 (34 months)
Common date range: 2021-08-01 00:00:00 to 2024-05-01 00:00:00
Aligned ETF data shape: (34, 35)
Aligned risk-free rate data shape: (34, 5)
Common dates: 34
ETF date coverage: 100.0%
Risk-free rate date coverage: 100.0%
[OK] Data alignment successful: ETF (34, 35), RF (34, 5)
DataPreprocessor initialized
Handling missing data using method: forward_fill
Missing values before processing: 0 (0.00%)
Missing values after processing: 0 (0.00%)
[OK] Missing data handling completed
Validating data quality for VT
Data quality validation passed for VT
[OK] Data quality validation: PASSED
Creating technical indicators for VT
DEBUG - Input data shape: (34, 5)
DEBUG - Input data columns: ['Open', 'High', 'Low', 'Close', 'Volume']
DEBUG - Data index type: <class 'pandas.core.indexes.datetimes.DatetimeIndex'>
DEBUG - Data dtypes: {'Open': dtype('float64'), 'High': dtype('float64'), 'Low': dtype('float64'), 'Close': dtype('float64'), 'Volume': dtype('int64')}
DEBUG - ta library imported successfully
DEBUG - High null values: 0
DEBUG - Low null values: 0
DEBUG - Close null values: 0
DEBUG - Volume null values: 0
DEBUG - Creating moving averages...
DEBUG - Creating SMA_10
DEBUG - Creating EMA_10
DEBUG - Successfully created MA indicators for period 10
DEBUG - Creating SMA_20
DEBUG - Creating EMA_20
DEBUG - Successfully created MA indicators for period 20
DEBUG - Creating SMA_50
DEBUG - Creating EMA_50
DEBUG - Successfully created MA indicators for period 50
DEBUG - Creating RSI...
DEBUG - RSI created successfully
DEBUG - Creating MACD...
DEBUG - MACD created successfully
DEBUG - Creating Bollinger Bands...
DEBUG - Bollinger Bands created successfully
DEBUG - Creating volume indicators...
DEBUG - Volume indicators created successfully
DEBUG - Creating ATR...
DEBUG - ATR created successfully
DEBUG - Creating price-based indicators...
DEBUG - Price-based indicators created successfully
Created 16 technical indicators
DEBUG - Final data shape: (34, 21)
DEBUG - New columns added: 16
[OK] Technical indicators created: (34, 21)
[RESOURCE] Resource Usage - after_data_fetching:
  Memory: 7.4GB / 7.8GB (94.2%)
  CPU: 38.6%
  Process Memory: 538.7MB
  Disk: 418.7GB / 465.1GB (90.0%)

[OK] ==============================================================================
PHASE COMPLETED: DATA FETCHING SYSTEM
End time: 2025-07-30 23:36:46
Duration: 0:00:16.658957
Phase Metrics:
  etf_data_available: True
  risk_free_data_available: True
  data_preprocessing_completed: True
  symbols_processed: 7
================================================================================
[MILESTONE] MILESTONE: Data Fetching Complete
   Timestamp: 2025-07-30 23:36:46
   Elapsed: 0:00:18.701863
   Details:
     etf_data_shape: (34, 35)
     risk_free_data_shape: (34, 5)
Testing TensorTrade instruments and portfolio setup...
Creating TensorTrade instruments and portfolio setup
Creating TensorTrade instruments for ETFs
Added USD as base instrument
USD instrument attributes: ['__class__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__format__', '__ge__', '__getattribute__', '__getstate__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__rmul__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', '__truediv__', '__weakref__', 'name', 'precision', 'symbol']
USD instrument type: <class 'tensortrade.oms.instruments.instrument.Instrument'>
USD symbol: USD
USD precision: 2
USD name: U.S. Dollar
Creating instruments for ETF symbols: ['VT', 'IAGG', 'REET', 'GLD', 'DBA', 'USO', 'UUP']
Created instrument for VT
  - Symbol: VT
  - Precision: 2
  - Name: VT
DEBUG - VT instrument attributes: ['__class__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__format__', '__ge__', '__getattribute__', '__getstate__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__rmul__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', '__truediv__', '__weakref__', 'name', 'precision', 'symbol']
DEBUG - VT instrument type: <class 'tensortrade.oms.instruments.instrument.Instrument'>
DEBUG - VT instrument missing 'id' attribute
DEBUG - Added 'id' attribute to VT instrument
DEBUG - VT instrument missing 'streams' attribute
DEBUG - Added 'streams' attribute to VT instrument
Created instrument for IAGG
  - Symbol: IAGG
  - Precision: 2
  - Name: IAGG
DEBUG - IAGG instrument attributes: ['__class__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__format__', '__ge__', '__getattribute__', '__getstate__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__rmul__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', '__truediv__', '__weakref__', 'name', 'precision', 'symbol']
DEBUG - IAGG instrument type: <class 'tensortrade.oms.instruments.instrument.Instrument'>
DEBUG - IAGG instrument missing 'id' attribute
DEBUG - Added 'id' attribute to IAGG instrument
DEBUG - IAGG instrument missing 'streams' attribute
DEBUG - Added 'streams' attribute to IAGG instrument
Created instrument for REET
  - Symbol: REET
  - Precision: 2
  - Name: REET
DEBUG - REET instrument attributes: ['__class__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__format__', '__ge__', '__getattribute__', '__getstate__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__rmul__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', '__truediv__', '__weakref__', 'name', 'precision', 'symbol']
DEBUG - REET instrument type: <class 'tensortrade.oms.instruments.instrument.Instrument'>
DEBUG - REET instrument missing 'id' attribute
DEBUG - Added 'id' attribute to REET instrument
DEBUG - REET instrument missing 'streams' attribute
DEBUG - Added 'streams' attribute to REET instrument
Created instrument for GLD
  - Symbol: GLD
  - Precision: 2
  - Name: GLD
DEBUG - GLD instrument attributes: ['__class__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__format__', '__ge__', '__getattribute__', '__getstate__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__rmul__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', '__truediv__', '__weakref__', 'name', 'precision', 'symbol']
DEBUG - GLD instrument type: <class 'tensortrade.oms.instruments.instrument.Instrument'>
DEBUG - GLD instrument missing 'id' attribute
DEBUG - Added 'id' attribute to GLD instrument
DEBUG - GLD instrument missing 'streams' attribute
DEBUG - Added 'streams' attribute to GLD instrument
Created instrument for DBA
  - Symbol: DBA
  - Precision: 2
  - Name: DBA
DEBUG - DBA instrument attributes: ['__class__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__format__', '__ge__', '__getattribute__', '__getstate__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__rmul__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', '__truediv__', '__weakref__', 'name', 'precision', 'symbol']
DEBUG - DBA instrument type: <class 'tensortrade.oms.instruments.instrument.Instrument'>
DEBUG - DBA instrument missing 'id' attribute
DEBUG - Added 'id' attribute to DBA instrument
DEBUG - DBA instrument missing 'streams' attribute
DEBUG - Added 'streams' attribute to DBA instrument
Created instrument for USO
  - Symbol: USO
  - Precision: 2
  - Name: USO
DEBUG - USO instrument attributes: ['__class__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__format__', '__ge__', '__getattribute__', '__getstate__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__rmul__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', '__truediv__', '__weakref__', 'name', 'precision', 'symbol']
DEBUG - USO instrument type: <class 'tensortrade.oms.instruments.instrument.Instrument'>
DEBUG - USO instrument missing 'id' attribute
DEBUG - Added 'id' attribute to USO instrument
DEBUG - USO instrument missing 'streams' attribute
DEBUG - Added 'streams' attribute to USO instrument
Created instrument for UUP
  - Symbol: UUP
  - Precision: 2
  - Name: UUP
DEBUG - UUP instrument attributes: ['__class__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__format__', '__ge__', '__getattribute__', '__getstate__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__rmul__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', '__truediv__', '__weakref__', 'name', 'precision', 'symbol']
DEBUG - UUP instrument type: <class 'tensortrade.oms.instruments.instrument.Instrument'>
DEBUG - UUP instrument missing 'id' attribute
DEBUG - Added 'id' attribute to UUP instrument
DEBUG - UUP instrument missing 'streams' attribute
DEBUG - Added 'streams' attribute to UUP instrument
Successfully created 8 instruments (including USD)
Initializing portfolio with wallets
Created USD wallet with initial balance: $100,000
Created VT wallet with 0 shares
Created IAGG wallet with 0 shares
Created REET wallet with 0 shares
Created GLD wallet with 0 shares
Created DBA wallet with 0 shares
Created USO wallet with 0 shares
Created UUP wallet with 0 shares
Portfolio creation has version compatibility issues
The Portfolio constructor expects instruments with 'id' attribute
This will need to be resolved in future iterations
DEBUG - Added 'exchanges' and 'orders' attributes to MockPortfolio
Created mock portfolio with 8 wallets
Mock portfolio base instrument: USD
Mock portfolio net worth: $100000.00
Portfolio base instrument: USD
Portfolio initial state:
  - Total wallets: 8
  - Base instrument: USD
  - Initial net worth: $100000.00
Portfolio validation successful
[OK] TensorTrade instruments created: 8 instruments
[OK] Portfolio initialized with 8 wallets
================================================================================
TENSORTRADE INSTRUMENTS AND PORTFOLIO SETUP COMPLETED SUCCESSFULLY
================================================================================
Testing TensorTrade data feeds and streams setup...
Creating comprehensive market data streams using TensorTrade Stream API
Creating streams for ETF symbols: ['VT', 'IAGG', 'REET', 'GLD', 'DBA', 'USO', 'UUP']
DataPreprocessor initialized
Creating streams for VT
Handling missing data using method: forward_fill
Missing values before processing: 0 (0.00%)
Missing values after processing: 0 (0.00%)
Creating technical indicators for VT
DEBUG - Input data shape: (34, 5)
DEBUG - Input data columns: ['Open', 'High', 'Low', 'Close', 'Volume']
DEBUG - Data index type: <class 'pandas.core.indexes.datetimes.DatetimeIndex'>
DEBUG - Data dtypes: {'Open': dtype('float64'), 'High': dtype('float64'), 'Low': dtype('float64'), 'Close': dtype('float64'), 'Volume': dtype('int64')}
DEBUG - ta library imported successfully
DEBUG - High null values: 0
DEBUG - Low null values: 0
DEBUG - Close null values: 0
DEBUG - Volume null values: 0
DEBUG - Creating moving averages...
DEBUG - Creating SMA_10
DEBUG - Creating EMA_10
DEBUG - Successfully created MA indicators for period 10
DEBUG - Creating SMA_20
DEBUG - Creating EMA_20
DEBUG - Successfully created MA indicators for period 20
DEBUG - Creating SMA_50
DEBUG - Creating EMA_50
DEBUG - Successfully created MA indicators for period 50
DEBUG - Creating RSI...
DEBUG - RSI created successfully
DEBUG - Creating MACD...
DEBUG - MACD created successfully
DEBUG - Creating Bollinger Bands...
DEBUG - Bollinger Bands created successfully
DEBUG - Creating volume indicators...
DEBUG - Volume indicators created successfully
DEBUG - Creating ATR...
DEBUG - ATR created successfully
DEBUG - Creating price-based indicators...
DEBUG - Price-based indicators created successfully
Created 16 technical indicators
DEBUG - Final data shape: (34, 21)
DEBUG - New columns added: 16
Successfully created streams for VT
Creating streams for IAGG
Handling missing data using method: forward_fill
Missing values before processing: 0 (0.00%)
Missing values after processing: 0 (0.00%)
Creating technical indicators for IAGG
DEBUG - Input data shape: (34, 5)
DEBUG - Input data columns: ['Open', 'High', 'Low', 'Close', 'Volume']
DEBUG - Data index type: <class 'pandas.core.indexes.datetimes.DatetimeIndex'>
DEBUG - Data dtypes: {'Open': dtype('float64'), 'High': dtype('float64'), 'Low': dtype('float64'), 'Close': dtype('float64'), 'Volume': dtype('int64')}
DEBUG - ta library imported successfully
DEBUG - High null values: 0
DEBUG - Low null values: 0
DEBUG - Close null values: 0
DEBUG - Volume null values: 0
DEBUG - Creating moving averages...
DEBUG - Creating SMA_10
DEBUG - Creating EMA_10
DEBUG - Successfully created MA indicators for period 10
DEBUG - Creating SMA_20
DEBUG - Creating EMA_20
DEBUG - Successfully created MA indicators for period 20
DEBUG - Creating SMA_50
DEBUG - Creating EMA_50
DEBUG - Successfully created MA indicators for period 50
DEBUG - Creating RSI...
DEBUG - RSI created successfully
DEBUG - Creating MACD...
DEBUG - MACD created successfully
DEBUG - Creating Bollinger Bands...
DEBUG - Bollinger Bands created successfully
DEBUG - Creating volume indicators...
DEBUG - Volume indicators created successfully
DEBUG - Creating ATR...
DEBUG - ATR created successfully
DEBUG - Creating price-based indicators...
DEBUG - Price-based indicators created successfully
Created 16 technical indicators
DEBUG - Final data shape: (34, 21)
DEBUG - New columns added: 16
Successfully created streams for IAGG
Creating streams for REET
Handling missing data using method: forward_fill
Missing values before processing: 0 (0.00%)
Missing values after processing: 0 (0.00%)
Creating technical indicators for REET
DEBUG - Input data shape: (34, 5)
DEBUG - Input data columns: ['Open', 'High', 'Low', 'Close', 'Volume']
DEBUG - Data index type: <class 'pandas.core.indexes.datetimes.DatetimeIndex'>
DEBUG - Data dtypes: {'Open': dtype('float64'), 'High': dtype('float64'), 'Low': dtype('float64'), 'Close': dtype('float64'), 'Volume': dtype('int64')}
DEBUG - ta library imported successfully
DEBUG - High null values: 0
DEBUG - Low null values: 0
DEBUG - Close null values: 0
DEBUG - Volume null values: 0
DEBUG - Creating moving averages...
DEBUG - Creating SMA_10
DEBUG - Creating EMA_10
DEBUG - Successfully created MA indicators for period 10
DEBUG - Creating SMA_20
DEBUG - Creating EMA_20
DEBUG - Successfully created MA indicators for period 20
DEBUG - Creating SMA_50
DEBUG - Creating EMA_50
DEBUG - Successfully created MA indicators for period 50
DEBUG - Creating RSI...
DEBUG - RSI created successfully
DEBUG - Creating MACD...
DEBUG - MACD created successfully
DEBUG - Creating Bollinger Bands...
DEBUG - Bollinger Bands created successfully
DEBUG - Creating volume indicators...
DEBUG - Volume indicators created successfully
DEBUG - Creating ATR...
DEBUG - ATR created successfully
DEBUG - Creating price-based indicators...
DEBUG - Price-based indicators created successfully
Created 16 technical indicators
DEBUG - Final data shape: (34, 21)
DEBUG - New columns added: 16
Successfully created streams for REET
Creating streams for GLD
Handling missing data using method: forward_fill
Missing values before processing: 0 (0.00%)
Missing values after processing: 0 (0.00%)
Creating technical indicators for GLD
DEBUG - Input data shape: (34, 5)
DEBUG - Input data columns: ['Open', 'High', 'Low', 'Close', 'Volume']
DEBUG - Data index type: <class 'pandas.core.indexes.datetimes.DatetimeIndex'>
DEBUG - Data dtypes: {'Open': dtype('float64'), 'High': dtype('float64'), 'Low': dtype('float64'), 'Close': dtype('float64'), 'Volume': dtype('int64')}
DEBUG - ta library imported successfully
DEBUG - High null values: 0
DEBUG - Low null values: 0
DEBUG - Close null values: 0
DEBUG - Volume null values: 0
DEBUG - Creating moving averages...
DEBUG - Creating SMA_10
DEBUG - Creating EMA_10
DEBUG - Successfully created MA indicators for period 10
DEBUG - Creating SMA_20
DEBUG - Creating EMA_20
DEBUG - Successfully created MA indicators for period 20
DEBUG - Creating SMA_50
DEBUG - Creating EMA_50
DEBUG - Successfully created MA indicators for period 50
DEBUG - Creating RSI...
DEBUG - RSI created successfully
DEBUG - Creating MACD...
DEBUG - MACD created successfully
DEBUG - Creating Bollinger Bands...
DEBUG - Bollinger Bands created successfully
DEBUG - Creating volume indicators...
DEBUG - Volume indicators created successfully
DEBUG - Creating ATR...
DEBUG - ATR created successfully
DEBUG - Creating price-based indicators...
DEBUG - Price-based indicators created successfully
Created 16 technical indicators
DEBUG - Final data shape: (34, 21)
DEBUG - New columns added: 16
Successfully created streams for GLD
Creating streams for DBA
Handling missing data using method: forward_fill
Missing values before processing: 0 (0.00%)
Missing values after processing: 0 (0.00%)
Creating technical indicators for DBA
DEBUG - Input data shape: (34, 5)
DEBUG - Input data columns: ['Open', 'High', 'Low', 'Close', 'Volume']
DEBUG - Data index type: <class 'pandas.core.indexes.datetimes.DatetimeIndex'>
DEBUG - Data dtypes: {'Open': dtype('float64'), 'High': dtype('float64'), 'Low': dtype('float64'), 'Close': dtype('float64'), 'Volume': dtype('int64')}
DEBUG - ta library imported successfully
DEBUG - High null values: 0
DEBUG - Low null values: 0
DEBUG - Close null values: 0
DEBUG - Volume null values: 0
DEBUG - Creating moving averages...
DEBUG - Creating SMA_10
DEBUG - Creating EMA_10
DEBUG - Successfully created MA indicators for period 10
DEBUG - Creating SMA_20
DEBUG - Creating EMA_20
DEBUG - Successfully created MA indicators for period 20
DEBUG - Creating SMA_50
DEBUG - Creating EMA_50
DEBUG - Successfully created MA indicators for period 50
DEBUG - Creating RSI...
DEBUG - RSI created successfully
DEBUG - Creating MACD...
DEBUG - MACD created successfully
DEBUG - Creating Bollinger Bands...
DEBUG - Bollinger Bands created successfully
DEBUG - Creating volume indicators...
DEBUG - Volume indicators created successfully
DEBUG - Creating ATR...
DEBUG - ATR created successfully
DEBUG - Creating price-based indicators...
DEBUG - Price-based indicators created successfully
Created 16 technical indicators
DEBUG - Final data shape: (34, 21)
DEBUG - New columns added: 16
Successfully created streams for DBA
Creating streams for USO
Handling missing data using method: forward_fill
Missing values before processing: 0 (0.00%)
Missing values after processing: 0 (0.00%)
Creating technical indicators for USO
DEBUG - Input data shape: (34, 5)
DEBUG - Input data columns: ['Open', 'High', 'Low', 'Close', 'Volume']
DEBUG - Data index type: <class 'pandas.core.indexes.datetimes.DatetimeIndex'>
DEBUG - Data dtypes: {'Open': dtype('float64'), 'High': dtype('float64'), 'Low': dtype('float64'), 'Close': dtype('float64'), 'Volume': dtype('int64')}
DEBUG - ta library imported successfully
DEBUG - High null values: 0
DEBUG - Low null values: 0
DEBUG - Close null values: 0
DEBUG - Volume null values: 0
DEBUG - Creating moving averages...
DEBUG - Creating SMA_10
DEBUG - Creating EMA_10
DEBUG - Successfully created MA indicators for period 10
DEBUG - Creating SMA_20
DEBUG - Creating EMA_20
DEBUG - Successfully created MA indicators for period 20
DEBUG - Creating SMA_50
DEBUG - Creating EMA_50
DEBUG - Successfully created MA indicators for period 50
DEBUG - Creating RSI...
DEBUG - RSI created successfully
DEBUG - Creating MACD...
DEBUG - MACD created successfully
DEBUG - Creating Bollinger Bands...
DEBUG - Bollinger Bands created successfully
DEBUG - Creating volume indicators...
DEBUG - Volume indicators created successfully
DEBUG - Creating ATR...
DEBUG - ATR created successfully
DEBUG - Creating price-based indicators...
DEBUG - Price-based indicators created successfully
Created 16 technical indicators
DEBUG - Final data shape: (34, 21)
DEBUG - New columns added: 16
Successfully created streams for USO
Creating streams for UUP
Handling missing data using method: forward_fill
Missing values before processing: 0 (0.00%)
Missing values after processing: 0 (0.00%)
Creating technical indicators for UUP
DEBUG - Input data shape: (34, 5)
DEBUG - Input data columns: ['Open', 'High', 'Low', 'Close', 'Volume']
DEBUG - Data index type: <class 'pandas.core.indexes.datetimes.DatetimeIndex'>
DEBUG - Data dtypes: {'Open': dtype('float64'), 'High': dtype('float64'), 'Low': dtype('float64'), 'Close': dtype('float64'), 'Volume': dtype('int64')}
DEBUG - ta library imported successfully
DEBUG - High null values: 0
DEBUG - Low null values: 0
DEBUG - Close null values: 0
DEBUG - Volume null values: 0
DEBUG - Creating moving averages...
DEBUG - Creating SMA_10
DEBUG - Creating EMA_10
DEBUG - Successfully created MA indicators for period 10
DEBUG - Creating SMA_20
DEBUG - Creating EMA_20
DEBUG - Successfully created MA indicators for period 20
DEBUG - Creating SMA_50
DEBUG - Creating EMA_50
DEBUG - Successfully created MA indicators for period 50
DEBUG - Creating RSI...
DEBUG - RSI created successfully
DEBUG - Creating MACD...
DEBUG - MACD created successfully
DEBUG - Creating Bollinger Bands...
DEBUG - Bollinger Bands created successfully
DEBUG - Creating volume indicators...
DEBUG - Volume indicators created successfully
DEBUG - Creating ATR...
DEBUG - ATR created successfully
DEBUG - Creating price-based indicators...
DEBUG - Price-based indicators created successfully
Created 16 technical indicators
DEBUG - Final data shape: (34, 21)
DEBUG - New columns added: 16
Successfully created streams for UUP
Creating risk-free rate stream for reward calculation using ^TNX data
Created risk-free rate stream with 34 data points
Market data streams creation completed successfully
Total streams created: 134
  - price_streams: 28 streams
  - volume_streams: 7 streams
  - technical_streams: 98 streams
  - risk_free_streams: 1 streams
[OK] Market data streams created successfully
[OK] Stream collections: ['price_streams', 'volume_streams', 'technical_streams', 'risk_free_streams']
Setting up DataFeed for observation space
Configuring observation window size: 12 months
Added 28 price streams to DataFeed
Added 7 volume streams to DataFeed
Added 98 technical indicator streams to DataFeed
Added 1 risk-free rate streams to DataFeed
Total streams for DataFeed: 134
Adding feature engineering streams for technical indicators
Added 21 feature engineering streams
Creating DataFeed with all market data streams
DataFeed *args approach failed: DataFeed.__init__() takes 2 positional arguments but 156 were given
DataFeed created using list approach
DataFeed created successfully
DataFeed contains 155 streams
Configuring DataFeed with observation window: 12
Compiling DataFeed for data flow preparation
DataFeed compilation successful
Testing DataFeed with feed.next() to verify data flow
DataFeed data flow verification completed
DataFeed setup completed successfully
DataFeed configuration summary:
  - Total streams: 155
  - Price streams: 28
  - Volume streams: 7
  - Technical streams: 98
  - Risk-free streams: 1
  - Feature streams: 21
  - Observation window: 12 months
  - Compilation status: Not compiled
[OK] DataFeed created successfully for observation space
[OK] DataFeed configured with 12 month observation window
================================================================================
TENSORTRADE DATA FEEDS AND STREAMS SETUP COMPLETED SUCCESSFULLY
================================================================================
Testing TensorTrade exchange with price streams and trading frictions setup...
Creating TensorTrade exchange with price streams and trading frictions
Configuring execution service with trading frictions
Transaction cost: 0.1%
Slippage range: 0.0% to 1.0%
Execution service configured successfully
Transaction costs and slippage will be applied during order execution
Execution service configuration:
  - Service type: Simulated execution
  - Transaction cost: 0.1% per trade
  - Slippage modeling: 0.0% to 1.0%
  - Order execution: Market orders with realistic delays
Creating price streams for ETF symbols: ['VT', 'IAGG', 'REET', 'GLD', 'DBA', 'USO', 'UUP']
Created price stream for VT: USD-VT
  - Data points: 34
  - Date range: 2021-08-01 00:00:00 to 2024-05-01 00:00:00
  - Price range: $74.13 to $108.78
Created price stream for IAGG: USD-IAGG
  - Data points: 34
  - Date range: 2021-08-01 00:00:00 to 2024-05-01 00:00:00
  - Price range: $43.07 to $49.92
Created price stream for REET: USD-REET
  - Data points: 34
  - Date range: 2021-08-01 00:00:00 to 2024-05-01 00:00:00
  - Price range: $19.06 to $27.24
Created price stream for GLD: USD-GLD
  - Data points: 34
  - Date range: 2021-08-01 00:00:00 to 2024-05-01 00:00:00
  - Price range: $151.91 to $215.30
Created price stream for DBA: USD-DBA
  - Data points: 34
  - Date range: 2021-08-01 00:00:00 to 2024-05-01 00:00:00
  - Price range: $17.48 to $24.14
Created price stream for USO: USD-USO
  - Data points: 34
  - Date range: 2021-08-01 00:00:00 to 2024-05-01 00:00:00
  - Price range: $47.91 to $85.47
Created price stream for UUP: USD-UUP
  - Data points: 34
  - Date range: 2021-08-01 00:00:00 to 2024-05-01 00:00:00
  - Price range: $22.19 to $27.73
Successfully created 7 price streams
Creating Exchange with execution service including trading frictions
Exchange created successfully with trading frictions
Exchange name: portfolio_exchange
Exchange service: execute_order (simulated with frictions)
Number of price streams: 7
Configured stream names: ['USD-VT', 'USD-IAGG', 'USD-REET', 'USD-GLD', 'USD-DBA', 'USD-USO', 'USD-UUP']
Trading frictions configuration:
  - Transaction costs: 0.1% per trade
  - Slippage range: 0.0% to 1.0%
  - Impact on order execution: Applied during trade execution
[OK] TensorTrade exchange created successfully with trading frictions
[OK] Exchange configured with price streams and execution service
[OK] Trading frictions configured: transaction costs and slippage
================================================================================
TENSORTRADE EXCHANGE WITH TRADING FRICTIONS SETUP COMPLETED SUCCESSFULLY
================================================================================
Testing complete TensorTrade environment assembly...
Assembling complete TensorTrade environment using tensortrade.env.default.create
Step 1: Creating TensorTrade instruments and portfolio
Creating TensorTrade instruments and portfolio setup
Creating TensorTrade instruments for ETFs
Added USD as base instrument
USD instrument attributes: ['__class__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__format__', '__ge__', '__getattribute__', '__getstate__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__rmul__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', '__truediv__', '__weakref__', 'name', 'precision', 'symbol']
USD instrument type: <class 'tensortrade.oms.instruments.instrument.Instrument'>
USD symbol: USD
USD precision: 2
USD name: U.S. Dollar
Creating instruments for ETF symbols: ['VT', 'IAGG', 'REET', 'GLD', 'DBA', 'USO', 'UUP']
Created instrument for VT
  - Symbol: VT
  - Precision: 2
  - Name: VT
DEBUG - VT instrument attributes: ['__class__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__format__', '__ge__', '__getattribute__', '__getstate__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__rmul__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', '__truediv__', '__weakref__', 'name', 'precision', 'symbol']
DEBUG - VT instrument type: <class 'tensortrade.oms.instruments.instrument.Instrument'>
DEBUG - VT instrument missing 'id' attribute
DEBUG - Added 'id' attribute to VT instrument
DEBUG - VT instrument missing 'streams' attribute
DEBUG - Added 'streams' attribute to VT instrument
Created instrument for IAGG
  - Symbol: IAGG
  - Precision: 2
  - Name: IAGG
DEBUG - IAGG instrument attributes: ['__class__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__format__', '__ge__', '__getattribute__', '__getstate__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__rmul__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', '__truediv__', '__weakref__', 'name', 'precision', 'symbol']
DEBUG - IAGG instrument type: <class 'tensortrade.oms.instruments.instrument.Instrument'>
DEBUG - IAGG instrument missing 'id' attribute
DEBUG - Added 'id' attribute to IAGG instrument
DEBUG - IAGG instrument missing 'streams' attribute
DEBUG - Added 'streams' attribute to IAGG instrument
Created instrument for REET
  - Symbol: REET
  - Precision: 2
  - Name: REET
DEBUG - REET instrument attributes: ['__class__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__format__', '__ge__', '__getattribute__', '__getstate__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__rmul__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', '__truediv__', '__weakref__', 'name', 'precision', 'symbol']
DEBUG - REET instrument type: <class 'tensortrade.oms.instruments.instrument.Instrument'>
DEBUG - REET instrument missing 'id' attribute
DEBUG - Added 'id' attribute to REET instrument
DEBUG - REET instrument missing 'streams' attribute
DEBUG - Added 'streams' attribute to REET instrument
Created instrument for GLD
  - Symbol: GLD
  - Precision: 2
  - Name: GLD
DEBUG - GLD instrument attributes: ['__class__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__format__', '__ge__', '__getattribute__', '__getstate__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__rmul__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', '__truediv__', '__weakref__', 'name', 'precision', 'symbol']
DEBUG - GLD instrument type: <class 'tensortrade.oms.instruments.instrument.Instrument'>
DEBUG - GLD instrument missing 'id' attribute
DEBUG - Added 'id' attribute to GLD instrument
DEBUG - GLD instrument missing 'streams' attribute
DEBUG - Added 'streams' attribute to GLD instrument
Created instrument for DBA
  - Symbol: DBA
  - Precision: 2
  - Name: DBA
DEBUG - DBA instrument attributes: ['__class__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__format__', '__ge__', '__getattribute__', '__getstate__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__rmul__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', '__truediv__', '__weakref__', 'name', 'precision', 'symbol']
DEBUG - DBA instrument type: <class 'tensortrade.oms.instruments.instrument.Instrument'>
DEBUG - DBA instrument missing 'id' attribute
DEBUG - Added 'id' attribute to DBA instrument
DEBUG - DBA instrument missing 'streams' attribute
DEBUG - Added 'streams' attribute to DBA instrument
Created instrument for USO
  - Symbol: USO
  - Precision: 2
  - Name: USO
DEBUG - USO instrument attributes: ['__class__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__format__', '__ge__', '__getattribute__', '__getstate__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__rmul__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', '__truediv__', '__weakref__', 'name', 'precision', 'symbol']
DEBUG - USO instrument type: <class 'tensortrade.oms.instruments.instrument.Instrument'>
DEBUG - USO instrument missing 'id' attribute
DEBUG - Added 'id' attribute to USO instrument
DEBUG - USO instrument missing 'streams' attribute
DEBUG - Added 'streams' attribute to USO instrument
Created instrument for UUP
  - Symbol: UUP
  - Precision: 2
  - Name: UUP
DEBUG - UUP instrument attributes: ['__class__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__format__', '__ge__', '__getattribute__', '__getstate__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__rmul__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', '__truediv__', '__weakref__', 'name', 'precision', 'symbol']
DEBUG - UUP instrument type: <class 'tensortrade.oms.instruments.instrument.Instrument'>
DEBUG - UUP instrument missing 'id' attribute
DEBUG - Added 'id' attribute to UUP instrument
DEBUG - UUP instrument missing 'streams' attribute
DEBUG - Added 'streams' attribute to UUP instrument
Successfully created 8 instruments (including USD)
Initializing portfolio with wallets
Created USD wallet with initial balance: $100,000
Created VT wallet with 0 shares
Created IAGG wallet with 0 shares
Created REET wallet with 0 shares
Created GLD wallet with 0 shares
Created DBA wallet with 0 shares
Created USO wallet with 0 shares
Created UUP wallet with 0 shares
Portfolio creation has version compatibility issues
The Portfolio constructor expects instruments with 'id' attribute
This will need to be resolved in future iterations
DEBUG - Added 'exchanges' and 'orders' attributes to MockPortfolio
Created mock portfolio with 8 wallets
Mock portfolio base instrument: USD
Mock portfolio net worth: $100000.00
Portfolio base instrument: USD
Portfolio initial state:
  - Total wallets: 8
  - Base instrument: USD
  - Initial net worth: $100000.00
Portfolio validation successful
[OK] Created 8 instruments and portfolio with 8 wallets
Step 2: Creating custom PortfolioWeightActionScheme
PortfolioWeightActionScheme initialized with 7 ETFs: ['VT', 'IAGG', 'REET', 'GLD', 'DBA', 'USO', 'UUP']
[OK] Created action scheme with 7 ETFs
[OK] Action space: Box(-1.0, 1.0, (7,), float32)
Step 3: Creating risk-free rate stream for reward scheme
[OK] Created risk-free rate stream with 34 data points
Step 4: Creating custom SharpeRatioRewardScheme
SharpeRatioRewardScheme initialized with lookback_window=12, max_drawdown_penalty=0.5, volatility_penalty=0.1, use_differential_sharpe=False
[OK] Created reward scheme with 12-period lookback window
Step 5: Creating comprehensive market data streams
Creating comprehensive market data streams using TensorTrade Stream API
Creating streams for ETF symbols: ['VT', 'IAGG', 'REET', 'GLD', 'DBA', 'USO', 'UUP']
DataPreprocessor initialized
Creating streams for VT
Handling missing data using method: forward_fill
Missing values before processing: 0 (0.00%)
Missing values after processing: 0 (0.00%)
Creating technical indicators for VT
DEBUG - Input data shape: (34, 5)
DEBUG - Input data columns: ['Open', 'High', 'Low', 'Close', 'Volume']
DEBUG - Data index type: <class 'pandas.core.indexes.datetimes.DatetimeIndex'>
DEBUG - Data dtypes: {'Open': dtype('float64'), 'High': dtype('float64'), 'Low': dtype('float64'), 'Close': dtype('float64'), 'Volume': dtype('int64')}
DEBUG - ta library imported successfully
DEBUG - High null values: 0
DEBUG - Low null values: 0
DEBUG - Close null values: 0
DEBUG - Volume null values: 0
DEBUG - Creating moving averages...
DEBUG - Creating SMA_10
DEBUG - Creating EMA_10
DEBUG - Successfully created MA indicators for period 10
DEBUG - Creating SMA_20
DEBUG - Creating EMA_20
DEBUG - Successfully created MA indicators for period 20
DEBUG - Creating SMA_50
DEBUG - Creating EMA_50
DEBUG - Successfully created MA indicators for period 50
DEBUG - Creating RSI...
DEBUG - RSI created successfully
DEBUG - Creating MACD...
DEBUG - MACD created successfully
DEBUG - Creating Bollinger Bands...
DEBUG - Bollinger Bands created successfully
DEBUG - Creating volume indicators...
DEBUG - Volume indicators created successfully
DEBUG - Creating ATR...
DEBUG - ATR created successfully
DEBUG - Creating price-based indicators...
DEBUG - Price-based indicators created successfully
Created 16 technical indicators
DEBUG - Final data shape: (34, 21)
DEBUG - New columns added: 16
Successfully created streams for VT
Creating streams for IAGG
Handling missing data using method: forward_fill
Missing values before processing: 0 (0.00%)
Missing values after processing: 0 (0.00%)
Creating technical indicators for IAGG
DEBUG - Input data shape: (34, 5)
DEBUG - Input data columns: ['Open', 'High', 'Low', 'Close', 'Volume']
DEBUG - Data index type: <class 'pandas.core.indexes.datetimes.DatetimeIndex'>
DEBUG - Data dtypes: {'Open': dtype('float64'), 'High': dtype('float64'), 'Low': dtype('float64'), 'Close': dtype('float64'), 'Volume': dtype('int64')}
DEBUG - ta library imported successfully
DEBUG - High null values: 0
DEBUG - Low null values: 0
DEBUG - Close null values: 0
DEBUG - Volume null values: 0
DEBUG - Creating moving averages...
DEBUG - Creating SMA_10
DEBUG - Creating EMA_10
DEBUG - Successfully created MA indicators for period 10
DEBUG - Creating SMA_20
DEBUG - Creating EMA_20
DEBUG - Successfully created MA indicators for period 20
DEBUG - Creating SMA_50
DEBUG - Creating EMA_50
DEBUG - Successfully created MA indicators for period 50
DEBUG - Creating RSI...
DEBUG - RSI created successfully
DEBUG - Creating MACD...
DEBUG - MACD created successfully
DEBUG - Creating Bollinger Bands...
DEBUG - Bollinger Bands created successfully
DEBUG - Creating volume indicators...
DEBUG - Volume indicators created successfully
DEBUG - Creating ATR...
DEBUG - ATR created successfully
DEBUG - Creating price-based indicators...
DEBUG - Price-based indicators created successfully
Created 16 technical indicators
DEBUG - Final data shape: (34, 21)
DEBUG - New columns added: 16
Successfully created streams for IAGG
Creating streams for REET
Handling missing data using method: forward_fill
Missing values before processing: 0 (0.00%)
Missing values after processing: 0 (0.00%)
Creating technical indicators for REET
DEBUG - Input data shape: (34, 5)
DEBUG - Input data columns: ['Open', 'High', 'Low', 'Close', 'Volume']
DEBUG - Data index type: <class 'pandas.core.indexes.datetimes.DatetimeIndex'>
DEBUG - Data dtypes: {'Open': dtype('float64'), 'High': dtype('float64'), 'Low': dtype('float64'), 'Close': dtype('float64'), 'Volume': dtype('int64')}
DEBUG - ta library imported successfully
DEBUG - High null values: 0
DEBUG - Low null values: 0
DEBUG - Close null values: 0
DEBUG - Volume null values: 0
DEBUG - Creating moving averages...
DEBUG - Creating SMA_10
DEBUG - Creating EMA_10
DEBUG - Successfully created MA indicators for period 10
DEBUG - Creating SMA_20
DEBUG - Creating EMA_20
DEBUG - Successfully created MA indicators for period 20
DEBUG - Creating SMA_50
DEBUG - Creating EMA_50
DEBUG - Successfully created MA indicators for period 50
DEBUG - Creating RSI...
DEBUG - RSI created successfully
DEBUG - Creating MACD...
DEBUG - MACD created successfully
DEBUG - Creating Bollinger Bands...
DEBUG - Bollinger Bands created successfully
DEBUG - Creating volume indicators...
DEBUG - Volume indicators created successfully
DEBUG - Creating ATR...
DEBUG - ATR created successfully
DEBUG - Creating price-based indicators...
DEBUG - Price-based indicators created successfully
Created 16 technical indicators
DEBUG - Final data shape: (34, 21)
DEBUG - New columns added: 16
Successfully created streams for REET
Creating streams for GLD
Handling missing data using method: forward_fill
Missing values before processing: 0 (0.00%)
Missing values after processing: 0 (0.00%)
Creating technical indicators for GLD
DEBUG - Input data shape: (34, 5)
DEBUG - Input data columns: ['Open', 'High', 'Low', 'Close', 'Volume']
DEBUG - Data index type: <class 'pandas.core.indexes.datetimes.DatetimeIndex'>
DEBUG - Data dtypes: {'Open': dtype('float64'), 'High': dtype('float64'), 'Low': dtype('float64'), 'Close': dtype('float64'), 'Volume': dtype('int64')}
DEBUG - ta library imported successfully
DEBUG - High null values: 0
DEBUG - Low null values: 0
DEBUG - Close null values: 0
DEBUG - Volume null values: 0
DEBUG - Creating moving averages...
DEBUG - Creating SMA_10
DEBUG - Creating EMA_10
DEBUG - Successfully created MA indicators for period 10
DEBUG - Creating SMA_20
DEBUG - Creating EMA_20
DEBUG - Successfully created MA indicators for period 20
DEBUG - Creating SMA_50
DEBUG - Creating EMA_50
DEBUG - Successfully created MA indicators for period 50
DEBUG - Creating RSI...
DEBUG - RSI created successfully
DEBUG - Creating MACD...
DEBUG - MACD created successfully
DEBUG - Creating Bollinger Bands...
DEBUG - Bollinger Bands created successfully
DEBUG - Creating volume indicators...
DEBUG - Volume indicators created successfully
DEBUG - Creating ATR...
DEBUG - ATR created successfully
DEBUG - Creating price-based indicators...
DEBUG - Price-based indicators created successfully
Created 16 technical indicators
DEBUG - Final data shape: (34, 21)
DEBUG - New columns added: 16
Successfully created streams for GLD
Creating streams for DBA
Handling missing data using method: forward_fill
Missing values before processing: 0 (0.00%)
Missing values after processing: 0 (0.00%)
Creating technical indicators for DBA
DEBUG - Input data shape: (34, 5)
DEBUG - Input data columns: ['Open', 'High', 'Low', 'Close', 'Volume']
DEBUG - Data index type: <class 'pandas.core.indexes.datetimes.DatetimeIndex'>
DEBUG - Data dtypes: {'Open': dtype('float64'), 'High': dtype('float64'), 'Low': dtype('float64'), 'Close': dtype('float64'), 'Volume': dtype('int64')}
DEBUG - ta library imported successfully
DEBUG - High null values: 0
DEBUG - Low null values: 0
DEBUG - Close null values: 0
DEBUG - Volume null values: 0
DEBUG - Creating moving averages...
DEBUG - Creating SMA_10
DEBUG - Creating EMA_10
DEBUG - Successfully created MA indicators for period 10
DEBUG - Creating SMA_20
DEBUG - Creating EMA_20
DEBUG - Successfully created MA indicators for period 20
DEBUG - Creating SMA_50
DEBUG - Creating EMA_50
DEBUG - Successfully created MA indicators for period 50
DEBUG - Creating RSI...
DEBUG - RSI created successfully
DEBUG - Creating MACD...
DEBUG - MACD created successfully
DEBUG - Creating Bollinger Bands...
DEBUG - Bollinger Bands created successfully
DEBUG - Creating volume indicators...
DEBUG - Volume indicators created successfully
DEBUG - Creating ATR...
DEBUG - ATR created successfully
DEBUG - Creating price-based indicators...
DEBUG - Price-based indicators created successfully
Created 16 technical indicators
DEBUG - Final data shape: (34, 21)
DEBUG - New columns added: 16
Successfully created streams for DBA
Creating streams for USO
Handling missing data using method: forward_fill
Missing values before processing: 0 (0.00%)
Missing values after processing: 0 (0.00%)
Creating technical indicators for USO
DEBUG - Input data shape: (34, 5)
DEBUG - Input data columns: ['Open', 'High', 'Low', 'Close', 'Volume']
DEBUG - Data index type: <class 'pandas.core.indexes.datetimes.DatetimeIndex'>
DEBUG - Data dtypes: {'Open': dtype('float64'), 'High': dtype('float64'), 'Low': dtype('float64'), 'Close': dtype('float64'), 'Volume': dtype('int64')}
DEBUG - ta library imported successfully
DEBUG - High null values: 0
DEBUG - Low null values: 0
DEBUG - Close null values: 0
DEBUG - Volume null values: 0
DEBUG - Creating moving averages...
DEBUG - Creating SMA_10
DEBUG - Creating EMA_10
DEBUG - Successfully created MA indicators for period 10
DEBUG - Creating SMA_20
DEBUG - Creating EMA_20
DEBUG - Successfully created MA indicators for period 20
DEBUG - Creating SMA_50
DEBUG - Creating EMA_50
DEBUG - Successfully created MA indicators for period 50
DEBUG - Creating RSI...
DEBUG - RSI created successfully
DEBUG - Creating MACD...
DEBUG - MACD created successfully
DEBUG - Creating Bollinger Bands...
DEBUG - Bollinger Bands created successfully
DEBUG - Creating volume indicators...
DEBUG - Volume indicators created successfully
DEBUG - Creating ATR...
DEBUG - ATR created successfully
DEBUG - Creating price-based indicators...
DEBUG - Price-based indicators created successfully
Created 16 technical indicators
DEBUG - Final data shape: (34, 21)
DEBUG - New columns added: 16
Successfully created streams for USO
Creating streams for UUP
Handling missing data using method: forward_fill
Missing values before processing: 0 (0.00%)
Missing values after processing: 0 (0.00%)
Creating technical indicators for UUP
DEBUG - Input data shape: (34, 5)
DEBUG - Input data columns: ['Open', 'High', 'Low', 'Close', 'Volume']
DEBUG - Data index type: <class 'pandas.core.indexes.datetimes.DatetimeIndex'>
DEBUG - Data dtypes: {'Open': dtype('float64'), 'High': dtype('float64'), 'Low': dtype('float64'), 'Close': dtype('float64'), 'Volume': dtype('int64')}
DEBUG - ta library imported successfully
DEBUG - High null values: 0
DEBUG - Low null values: 0
DEBUG - Close null values: 0
DEBUG - Volume null values: 0
DEBUG - Creating moving averages...
DEBUG - Creating SMA_10
DEBUG - Creating EMA_10
DEBUG - Successfully created MA indicators for period 10
DEBUG - Creating SMA_20
DEBUG - Creating EMA_20
DEBUG - Successfully created MA indicators for period 20
DEBUG - Creating SMA_50
DEBUG - Creating EMA_50
DEBUG - Successfully created MA indicators for period 50
DEBUG - Creating RSI...
DEBUG - RSI created successfully
DEBUG - Creating MACD...
DEBUG - MACD created successfully
DEBUG - Creating Bollinger Bands...
DEBUG - Bollinger Bands created successfully
DEBUG - Creating volume indicators...
DEBUG - Volume indicators created successfully
DEBUG - Creating ATR...
DEBUG - ATR created successfully
DEBUG - Creating price-based indicators...
DEBUG - Price-based indicators created successfully
Created 16 technical indicators
DEBUG - Final data shape: (34, 21)
DEBUG - New columns added: 16
Successfully created streams for UUP
Creating risk-free rate stream for reward calculation using ^TNX data
Created risk-free rate stream with 34 data points
Market data streams creation completed successfully
Total streams created: 134
  - price_streams: 28 streams
  - volume_streams: 7 streams
  - technical_streams: 98 streams
  - risk_free_streams: 1 streams
[OK] Created stream collections: ['price_streams', 'volume_streams', 'technical_streams', 'risk_free_streams']
Step 6: Creating DataFeed for observation space
Setting up DataFeed for observation space
Configuring observation window size: 12 months
Added 28 price streams to DataFeed
Added 7 volume streams to DataFeed
Added 98 technical indicator streams to DataFeed
Added 1 risk-free rate streams to DataFeed
Total streams for DataFeed: 134
Adding feature engineering streams for technical indicators
Added 21 feature engineering streams
Creating DataFeed with all market data streams
DataFeed *args approach failed: DataFeed.__init__() takes 2 positional arguments but 156 were given
DataFeed created using list approach
DataFeed created successfully
DataFeed contains 155 streams
Configuring DataFeed with observation window: 12
Compiling DataFeed for data flow preparation
DataFeed compilation successful
Testing DataFeed with feed.next() to verify data flow
DataFeed data flow verification completed
DataFeed setup completed successfully
DataFeed configuration summary:
  - Total streams: 155
  - Price streams: 28
  - Volume streams: 7
  - Technical streams: 98
  - Risk-free streams: 1
  - Feature streams: 21
  - Observation window: 12 months
  - Compilation status: Not compiled
[OK] Created DataFeed with 12-month observation window
Step 7: Creating exchange with trading frictions
Creating TensorTrade exchange with price streams and trading frictions
Configuring execution service with trading frictions
Transaction cost: 0.1%
Slippage range: 0.0% to 1.0%
Execution service configured successfully
Transaction costs and slippage will be applied during order execution
Execution service configuration:
  - Service type: Simulated execution
  - Transaction cost: 0.1% per trade
  - Slippage modeling: 0.0% to 1.0%
  - Order execution: Market orders with realistic delays
Creating price streams for ETF symbols: ['VT', 'IAGG', 'REET', 'GLD', 'DBA', 'USO', 'UUP']
Created price stream for VT: USD-VT
  - Data points: 34
  - Date range: 2021-08-01 00:00:00 to 2024-05-01 00:00:00
  - Price range: $74.13 to $108.78
Created price stream for IAGG: USD-IAGG
  - Data points: 34
  - Date range: 2021-08-01 00:00:00 to 2024-05-01 00:00:00
  - Price range: $43.07 to $49.92
Created price stream for REET: USD-REET
  - Data points: 34
  - Date range: 2021-08-01 00:00:00 to 2024-05-01 00:00:00
  - Price range: $19.06 to $27.24
Created price stream for GLD: USD-GLD
  - Data points: 34
  - Date range: 2021-08-01 00:00:00 to 2024-05-01 00:00:00
  - Price range: $151.91 to $215.30
Created price stream for DBA: USD-DBA
  - Data points: 34
  - Date range: 2021-08-01 00:00:00 to 2024-05-01 00:00:00
  - Price range: $17.48 to $24.14
Created price stream for USO: USD-USO
  - Data points: 34
  - Date range: 2021-08-01 00:00:00 to 2024-05-01 00:00:00
  - Price range: $47.91 to $85.47
Created price stream for UUP: USD-UUP
  - Data points: 34
  - Date range: 2021-08-01 00:00:00 to 2024-05-01 00:00:00
  - Price range: $22.19 to $27.73
Successfully created 7 price streams
Creating Exchange with execution service including trading frictions
Exchange created successfully with trading frictions
Exchange name: portfolio_exchange
Exchange service: execute_order (simulated with frictions)
Number of price streams: 7
Configured stream names: ['USD-VT', 'USD-IAGG', 'USD-REET', 'USD-GLD', 'USD-DBA', 'USD-USO', 'USD-UUP']
Trading frictions configuration:
  - Transaction costs: 0.1% per trade
  - Slippage range: 0.0% to 1.0%
  - Impact on order execution: Applied during trade execution
[OK] Created exchange with trading frictions
Step 8: Configuring environment parameters
Environment configuration:
  - portfolio: MockPortfolio
  - action_scheme: PortfolioWeightActionScheme
  - reward_scheme: SharpeRatioRewardScheme
  - feed: DataFeed
  - exchange: Exchange
  - window_size: 12
  - max_allowed_loss: 0.6
  - enable_logger: True
Step 9: Adding renderer configuration for monitoring
[OK] Added ScreenLogger renderer for monitoring
Step 10: Creating complete TensorTrade environment using default.create()
Failed to create environment with default.create(): 'Instrument' object has no attribute 'streams'
Attempting alternative environment creation approach...
Manual environment creation also failed: 'DataFeed' object has no attribute 'observation_space'
Creating custom environment wrapper for compatibility...
Creating minimal environment for testing purposes
Creating minimal testing environment as fallback
Minimal trading environment initialized
[OK] Minimal testing environment created as fallback
Step 11: Testing environment creation and component integration
[OK] Environment has required attribute: action_space
[OK] Environment has required attribute: observation_space
[OK] Environment has required attribute: reset
[OK] Environment has required attribute: step
[OK] Environment reset successful, observation shape: (50,)
[OK] Action space: Box(-1.0, 1.0, (7,), float32)
[OK] Observation space: Box(-inf, inf, (50,), float32)
[OK] Basic environment functionality test passed
============================================================
COMPLETE TENSORTRADE ENVIRONMENT SUMMARY
============================================================
Environment type: MinimalTradingEnvironment
Portfolio: 8 wallets with $100,000 initial cash
Action scheme: portfolio-weights with 7 ETFs
Reward scheme: sharpe-ratio with 12-period window
Data feed: 4 stream collections
Exchange: Exchange with trading frictions
Window size: 12 months
Max allowed loss: 60.0%
Logger enabled: True
============================================================
  - Action scheme: PortfolioWeightActionScheme
  - Reward scheme: SharpeRatioRewardScheme
Failed to create complete TensorTrade environment: 'MinimalTradingEnvironment' object has no attribute 'observer'
TensorTrade environment creation failed: Complete TensorTrade environment creation failed: 'MinimalTradingEnvironment' object has no attribute 'observer'
Falling back to custom PortfolioTradingEnvironment...
Creating custom PortfolioTradingEnvironment...
Creating complete PortfolioTradingEnvironment
PortfolioWeightActionScheme initialized with 0 ETFs: []
Expected 7 ETFs, got 0
SharpeRatioRewardScheme initialized with lookback_window=12, max_drawdown_penalty=0.5, volatility_penalty=0.1, use_differential_sharpe=False
PortfolioTradingEnvironment initialized with 22 steps
Action space: Box(-1.0, 1.0, (7,), float32)
Observation space: Box(-inf, inf, (155,), float32)
PortfolioTradingEnvironment created successfully
Environment ready for training with 22 steps
Environment wrapped with GymnasiumWrapper for Stable Baselines3 compatibility
[OK] Custom PortfolioTradingEnvironment created successfully
Testing custom environment functionality...
Testing environment functionality with gym interface
Test 1: Testing environment.reset() method
[OK] Environment reset successful
[FAIL] Environment reset failed: 'tuple' object has no attribute 'shape'
Custom environment functionality testing had issues
PPO agent training skipped - environment not available or functionality test failed

[PHASE] ==============================================================================
PHASE START: SYSTEM FINALIZATION
Description: Creating final summary and cleanup
Start time: 2025-07-30 23:36:47
================================================================================
[RESOURCE] Resource Usage - system_finalization:
  Memory: 7.4GB / 7.8GB (94.2%)
  CPU: 42.4%
  Process Memory: 539.7MB
  Disk: 418.7GB / 465.1GB (90.0%)
Creating comprehensive system execution summary...
